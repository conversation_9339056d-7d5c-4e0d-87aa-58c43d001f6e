#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端 - 单点登录系统GUI客户端
使用tkinter图形界面连接Flask服务端
"""

import requests
import time
import json
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime
import threading
import platform
import uuid
import hashlib

class LoginClient:
    def __init__(self, server_url='http://127.0.0.1:5000'):
        self.server_url = server_url
        self.session = requests.Session()  # 使用Session保持cookie
        self.activation_code = None
        self.session_id = None
        self.device_code = self.generate_device_code()
        self.encryption_token = None  # 存储服务端返回的加密令牌
        self.is_online = False
        self.heartbeat_thread = None
        self.heartbeat_running = False

    def generate_device_code(self):
        """生成设备特征码"""
        try:
            # 获取系统信息
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node(),
                'mac': hex(uuid.getnode())  # MAC地址
            }

            # 生成唯一设备码
            device_string = ''.join(str(v) for v in system_info.values())
            device_hash = hashlib.md5(device_string.encode()).hexdigest()
            return device_hash[:16]  # 取前16位作为设备码
        except Exception as e:
            # 如果获取失败，使用随机UUID
            return str(uuid.uuid4())[:16]

    def login(self, activation_code, callback=None):
        """
        使用激活码登录到服务端
        """
        try:
            url = f"{self.server_url}/login"
            data = {
                'activation_code': activation_code,
                'device_code': self.device_code
            }

            response = self.session.post(url, json=data)
            result = response.json()

            if result.get('success'):
                self.activation_code = activation_code
                self.session_id = result.get('session_id')
                self.encryption_token = result.get('encryption_token')  # 存储加密令牌
                self.is_online = True
                if callback:
                    callback('success', f"登录成功! 激活码: {activation_code[:8]}... 令牌: {self.encryption_token[:16] if self.encryption_token else 'N/A'}...")
                return True
            else:
                message = result.get('message', '未知错误')
                if callback:
                    callback('error', f"登录失败: {message}")
                return False

        except requests.exceptions.ConnectionError:
            if callback:
                callback('error', "无法连接到服务端，请确保服务端已启动")
            return False
        except Exception as e:
            if callback:
                callback('error', f"登录异常: {str(e)}")
            return False

    def logout(self, callback=None):
        """
        从服务端注销
        """
        try:
            url = f"{self.server_url}/logout"

            response = self.session.post(url)
            result = response.json()

            if result.get('success'):
                self.activation_code = None
                self.session_id = None
                self.encryption_token = None  # 清空加密令牌
                self.is_online = False
                self.heartbeat_running = False
                if callback:
                    callback('success', "注销成功，令牌已销毁")
                return True
            else:
                message = result.get('message', '未知错误')
                if callback:
                    callback('error', f"注销失败: {message}")
                return False

        except Exception as e:
            if callback:
                callback('error', f"注销异常: {str(e)}")
            return False

    def check_status(self, callback=None):
        """
        检查在线状态
        """
        try:
            url = f"{self.server_url}/status"

            response = self.session.get(url)
            result = response.json()

            if result.get('online'):
                self.is_online = True
                if callback:
                    callback('online', "在线状态正常")
                return True
            else:
                self.is_online = False
                self.heartbeat_running = False
                message = result.get('message', '未知原因')
                if '其他设备' in message:
                    if callback:
                        callback('kicked', "账号已在其他设备登录，被强制下线")
                else:
                    if callback:
                        callback('offline', f"离线状态: {message}")
                return False

        except Exception as e:
            self.is_online = False
            if callback:
                callback('error', f"状态检查异常: {str(e)}")
            return False

    def send_heartbeat(self, callback=None):
        """
        发送心跳保持连接
        """
        try:
            url = f"{self.server_url}/heartbeat"

            response = self.session.post(url)
            result = response.json()

            if result.get('success'):
                if callback:
                    callback('success', "心跳正常")
                return True
            else:
                self.is_online = False
                self.heartbeat_running = False
                message = result.get('message', '未知错误')
                if callback:
                    callback('error', f"心跳失败: {message}")
                return False

        except Exception as e:
            self.is_online = False
            if callback:
                callback('error', f"心跳异常: {str(e)}")
            return False

    def get_online_users(self, callback=None):
        """
        获取在线用户列表
        """
        try:
            url = f"{self.server_url}/online_users"

            response = self.session.get(url)
            result = response.json()

            if result.get('success'):
                users_info = []
                for user in result.get('users', []):
                    users_info.append(f"🔑 {user['activation_code']} | 设备: {user['device_code']} | 登录: {user['login_time']} | 在线: {user['online_duration']}秒")

                if callback:
                    callback('success', {
                        'count': result.get('online_count', 0),
                        'users': users_info
                    })
                return True
            else:
                message = result.get('message', '未知错误')
                if callback:
                    callback('error', f"获取在线用户失败: {message}")
                return False

        except Exception as e:
            if callback:
                callback('error', f"获取在线用户异常: {str(e)}")
            return False

    def verify_encryption_token(self, callback=None):
        """
        验证加密令牌是否有效
        """
        try:
            if not self.activation_code or not self.encryption_token:
                if callback:
                    callback('error', "缺少激活码或加密令牌")
                return False

            url = f"{self.server_url}/verify_token"
            data = {
                'activation_code': self.activation_code,
                'encryption_token': self.encryption_token
            }

            response = self.session.post(url, json=data)
            result = response.json()

            if result.get('success'):
                if callback:
                    callback('success', "令牌验证成功")
                return True
            else:
                message = result.get('message', '未知错误')
                if callback:
                    callback('error', f"令牌验证失败: {message}")
                return False

        except Exception as e:
            if callback:
                callback('error', f"令牌验证异常: {str(e)}")
            return False

    def start_heartbeat(self, callback=None):
        """
        启动心跳线程
        """
        if not self.heartbeat_running and self.is_online:
            self.heartbeat_running = True
            self.heartbeat_thread = threading.Thread(target=self._heartbeat_worker, args=(callback,))
            self.heartbeat_thread.daemon = True
            self.heartbeat_thread.start()

    def stop_heartbeat(self):
        """
        停止心跳线程
        """
        self.heartbeat_running = False

    def _heartbeat_worker(self, callback):
        """
        心跳工作线程
        """
        while self.heartbeat_running and self.is_online:
            time.sleep(5)  # 每5秒检查一次
            if self.heartbeat_running:
                self.check_status(callback)

class LoginGUI:
    def __init__(self):
        self.client = LoginClient()
        self.root = tk.Tk()
        self.root.title("🔐 激活码登录系统")
        self.root.geometry("650x650")
        self.root.resizable(True, True)
        self.root.minsize(600, 600)

        # 设置窗口图标和样式
        try:
            self.root.iconbitmap(default="")  # 可以添加图标文件
        except:
            pass

        # 设置窗口居中
        self.center_window()

        # 创建界面
        self.create_widgets()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """创建GUI组件"""
        # 配置根窗口网格
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # 主框架
        main_frame = ttk.Frame(self.root, padding="25")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, columnspan=2, pady=(0, 25))

        title_label = ttk.Label(title_frame, text="🔐 激活码登录系统",
                               font=("Microsoft YaHei UI", 18, "bold"),
                               foreground="#2c3e50")
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text="基于设备特征码的安全登录",
                                  font=("Microsoft YaHei UI", 10),
                                  foreground="#7f8c8d")
        subtitle_label.pack(pady=(5, 0))

        # 设备信息显示
        device_info_frame = ttk.LabelFrame(main_frame, text="📱 设备信息", padding="15")
        device_info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        ttk.Label(device_info_frame, text="设备特征码:",
                 font=("Microsoft YaHei UI", 10, "bold")).grid(row=0, column=0, sticky=tk.W, pady=5)
        device_code_label = ttk.Label(device_info_frame, text=f"{self.client.device_code}",
                                     font=("Consolas", 11), foreground="#3498db")
        device_code_label.grid(row=0, column=1, sticky=tk.W, pady=5, padx=(15, 0))

        # 登录区域
        login_frame = ttk.LabelFrame(main_frame, text="🔑 激活码登录", padding="15")
        login_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        ttk.Label(login_frame, text="激活码:",
                 font=("Microsoft YaHei UI", 10, "bold")).grid(row=0, column=0, sticky=tk.W, pady=8)
        self.activation_code_var = tk.StringVar()
        self.activation_code_entry = ttk.Entry(login_frame, textvariable=self.activation_code_var,
                                              width=35, font=("Consolas", 11))
        self.activation_code_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=8, padx=(15, 0))

        # 登录和注销按钮
        button_container = ttk.Frame(login_frame)
        button_container.grid(row=1, column=0, columnspan=2, pady=15)

        self.login_btn = ttk.Button(button_container, text="🚀 登录", command=self.login)
        self.login_btn.grid(row=0, column=0, padx=(0, 10), ipadx=20, ipady=5)

        self.logout_btn = ttk.Button(button_container, text="🚪 注销登录", command=self.logout, state="disabled")
        self.logout_btn.grid(row=0, column=1, padx=(0, 10), ipadx=20, ipady=5)

        self.token_btn = ttk.Button(button_container, text="🔐 验证令牌", command=self.verify_token, state="disabled")
        self.token_btn.grid(row=0, column=2, ipadx=20, ipady=5)

        # 状态区域
        status_frame = ttk.LabelFrame(main_frame, text="📊 连接状态", padding="15")
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        self.status_var = tk.StringVar(value="🔴 未连接")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var,
                                     font=("Microsoft YaHei UI", 12, "bold"),
                                     foreground="#e74c3c")
        self.status_label.grid(row=0, column=0, sticky=tk.W, pady=5)



        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="系统日志", padding="15")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))

        # 日志文本框 - 调整为15行高度，确保显示完整
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=15,
            width=75,
            font=("Consolas", 9),
            bg="#f8f9fa",
            fg="#333333",
            wrap=tk.WORD
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=5)

        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        login_frame.columnconfigure(1, weight=1)
        device_info_frame.columnconfigure(1, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 初始化日志
        self.add_log("🚀 系统启动完成，请输入激活码登录")
        self.add_log(f"🔧 当前设备特征码: {self.client.device_code}")
        self.add_log("💡 提示: 输入激活码后按回车键或点击登录按钮")

        # 绑定回车键登录
        self.activation_code_entry.bind('<Return>', lambda event: self.login())

        # 设置焦点到激活码输入框
        self.activation_code_entry.focus_set()
    def add_log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"

        # 插入日志并设置颜色
        self.log_text.insert(tk.END, log_message)

        # 根据消息类型设置不同颜色
        if "✅" in message or "成功" in message:
            # 成功消息 - 绿色
            start_line = self.log_text.index(tk.END + "-2l linestart")
            end_line = self.log_text.index(tk.END + "-1l lineend")
            self.log_text.tag_add("success", start_line, end_line)
            self.log_text.tag_config("success", foreground="#27ae60")
        elif "❌" in message or "失败" in message or "错误" in message:
            # 错误消息 - 红色
            start_line = self.log_text.index(tk.END + "-2l linestart")
            end_line = self.log_text.index(tk.END + "-1l lineend")
            self.log_text.tag_add("error", start_line, end_line)
            self.log_text.tag_config("error", foreground="#e74c3c")
        elif "⚠️" in message or "警告" in message:
            # 警告消息 - 橙色
            start_line = self.log_text.index(tk.END + "-2l linestart")
            end_line = self.log_text.index(tk.END + "-1l lineend")
            self.log_text.tag_add("warning", start_line, end_line)
            self.log_text.tag_config("warning", foreground="#f39c12")
        elif "💡" in message or "提示" in message:
            # 提示消息 - 蓝色
            start_line = self.log_text.index(tk.END + "-2l linestart")
            end_line = self.log_text.index(tk.END + "-1l lineend")
            self.log_text.tag_add("info", start_line, end_line)
            self.log_text.tag_config("info", foreground="#3498db")

        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def login(self):
        """登录操作"""
        activation_code = self.activation_code_var.get().strip()
        if not activation_code:
            messagebox.showerror("错误", "请输入激活码")
            return

        self.login_btn.config(state="disabled", text="登录中...")
        self.add_log(f"尝试使用激活码登录: {activation_code[:8]}...")

        # 在新线程中执行登录
        threading.Thread(target=self._login_worker, args=(activation_code,), daemon=True).start()

    def _login_worker(self, activation_code):
        """登录工作线程"""
        def callback(status, message):
            self.root.after(0, self._handle_login_result, status, message)

        self.client.login(activation_code, callback=callback)

    def _handle_login_result(self, status, message):
        """处理登录结果"""
        self.add_log(message)

        if status == 'success':
            self.status_var.set(f"🟢 已登录: {self.client.activation_code[:8]}...")
            self.status_label.config(foreground="#27ae60")
            self.login_btn.config(state="disabled", text="✅ 已登录")
            self.activation_code_entry.config(state="disabled")

            # 启用注销按钮和令牌验证按钮
            self.logout_btn.config(state="normal")
            self.token_btn.config(state="normal")

            # 自动开启心跳
            self.start_heartbeat()

        else:
            self.login_btn.config(state="normal", text="🚀 登录")

    def logout(self):
        """注销操作"""
        if not self.client.is_online:
            return

        self.add_log("正在注销...")

        # 在新线程中执行注销
        threading.Thread(target=self._logout_worker, daemon=True).start()

    def _logout_worker(self):
        """注销工作线程"""
        def callback(status, message):
            self.root.after(0, self._handle_logout_result, status, message)

        self.client.logout(callback=callback)

    def _handle_logout_result(self, status, message):
        """处理注销结果"""
        self.add_log(message)
        self.reset_ui()



    def start_heartbeat(self):
        """开启心跳"""
        if not self.client.is_online:
            return

        def callback(status, message):
            self.root.after(0, self._handle_heartbeat_result, status, message)

        self.client.start_heartbeat(callback=callback)
        self.add_log("🔄 心跳监控已开启")

    def stop_heartbeat(self):
        """停止心跳"""
        self.client.stop_heartbeat()
        self.add_log("⏹️ 心跳监控已停止")

    def _clear_device_code_on_disconnect(self):
        """断开连接时清空服务端设备特征码"""
        if not self.client.activation_code:
            return

        def clear_worker():
            try:
                # 发送注销请求来清空特征码
                url = f"{self.client.server_url}/logout"
                response = self.client.session.post(url)
                if response.status_code == 200:
                    self.add_log("� 已清空服务端设备特征码")
                else:
                    self.add_log("⚠️ 清空设备特征码失败")
            except Exception as e:
                self.add_log(f"⚠️ 清空设备特征码异常: {str(e)}")

        # 在后台线程中执行清空操作
        threading.Thread(target=clear_worker, daemon=True).start()

    def _handle_heartbeat_result(self, status, message):
        """处理心跳结果"""
        if status == 'online':
            # 正常在线，不显示日志避免刷屏
            pass
        elif status == 'kicked':
            self.add_log("⚠️ " + message)
            # 先断开连接并重置UI状态
            self.reset_ui()
            # 然后在新线程中显示弹框，避免阻塞UI更新
            def show_warning():
                messagebox.showwarning("账号被踢下线", message)
            threading.Thread(target=show_warning, daemon=True).start()
        elif status == 'error' or status == 'offline':
            self.add_log("❌ " + message)
            self.reset_ui()

    def verify_token(self):
        """验证加密令牌"""
        if not self.client.is_online:
            self.add_log("❌ 请先登录后再验证令牌")
            return

        def callback(status, message):
            self.add_log(message)

        self.client.verify_encryption_token(callback=callback)

    def reset_ui(self):
        """重置UI状态"""
        self.client.stop_heartbeat()
        self.status_var.set("🔴 未连接")
        self.status_label.config(foreground="#e74c3c")
        self.login_btn.config(state="normal", text="🚀 登录")
        self.activation_code_entry.config(state="normal")
        self.logout_btn.config(state="disabled")
        self.token_btn.config(state="disabled")  # 禁用令牌验证按钮
        # 重新设置焦点到激活码输入框
        self.activation_code_entry.focus_set()

    def on_closing(self):
        """窗口关闭事件"""
        if self.client.is_online:
            # 正常关闭时主动注销
            self.client.logout()
        self.client.stop_heartbeat()
        self.root.destroy()

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主程序入口"""
    app = LoginGUI()
    app.run()

if __name__ == '__main__':
    main()